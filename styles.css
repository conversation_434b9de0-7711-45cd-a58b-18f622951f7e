/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    overflow: hidden;
}

/* Dashboard Container */
.dashboard-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.dashboard-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-content h1 {
    color: #2c3e50;
    font-size: 1.8rem;
    font-weight: 600;
}

.header-content h1 i {
    margin-right: 0.5rem;
    color: #3498db;
}

.header-info {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.current-time, .user-info {
    color: #7f8c8d;
    font-size: 0.9rem;
}

.user-info i {
    margin-right: 0.5rem;
}

/* Main Dashboard */
.dashboard-main {
    flex: 1;
    display: grid;
    grid-template-columns: 350px 1fr 350px;
    gap: 1rem;
    padding: 1rem;
    overflow: hidden;
}

/* Side Panels */
.left-panels, .right-panels {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.panel {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.panel:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.panel-header {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-header h2 {
    font-size: 1.1rem;
    font-weight: 600;
}

.panel-header i {
    margin-right: 0.5rem;
}

.panel-content {
    padding: 1.5rem;
}

/* Overview Panel */
.overview-panel {
    flex: 1;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.stat-card {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 8px;
    transition: transform 0.2s ease;
}

.stat-card:hover {
    transform: scale(1.02);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    font-size: 1.2rem;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: bold;
    color: #2c3e50;
}

.stat-label {
    font-size: 0.85rem;
    color: #7f8c8d;
    margin-top: 0.25rem;
}

/* Supervision Panel */
.supervision-panel {
    flex: 1;
}

.time-toggle {
    display: flex;
    gap: 0.5rem;
}

.toggle-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.85rem;
}

.toggle-btn.active {
    background: rgba(255, 255, 255, 0.3);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.supervision-stats {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.supervision-item {
    text-align: center;
}

.supervision-label {
    font-size: 0.9rem;
    color: #7f8c8d;
    margin-bottom: 0.5rem;
}

.supervision-value {
    font-size: 2rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.supervision-chart {
    height: 60px;
    background: linear-gradient(135deg, #e8f4fd, #d1ecf1);
    border-radius: 6px;
    position: relative;
    overflow: hidden;
}

/* Map Container */
.map-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.map-header {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.map-header h2 {
    font-size: 1.2rem;
    font-weight: 600;
}

.map-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.view-toggle {
    display: flex;
    gap: 0.5rem;
}

.filter-controls select {
    padding: 0.5rem;
    border: none;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    cursor: pointer;
}

.filter-controls select option {
    background: #27ae60;
    color: white;
}

.map {
    flex: 1;
    min-height: 400px;
}

/* Survey Panel */
.survey-panel {
    flex: 1;
}

.survey-stats {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.survey-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 8px;
}

.survey-label {
    font-size: 0.9rem;
    color: #7f8c8d;
}

.survey-value {
    font-size: 1.2rem;
    font-weight: bold;
    color: #2c3e50;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #ecf0f1;
    border-radius: 3px;
    margin-top: 0.5rem;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #27ae60, #229954);
    border-radius: 3px;
    transition: width 0.3s ease;
}

/* Status Panel */
.status-panel {
    flex: 1;
}

.status-tabs {
    display: flex;
    margin-bottom: 1rem;
}

.tab-btn {
    flex: 1;
    padding: 0.75rem;
    border: none;
    background: #ecf0f1;
    color: #7f8c8d;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 6px 6px 0 0;
}

.tab-btn.active {
    background: #3498db;
    color: white;
}

.status-lists {
    position: relative;
    height: 300px;
    overflow: hidden;
}

.status-list {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow-y: auto;
    opacity: 0;
    transform: translateX(20px);
    transition: all 0.3s ease;
}

.status-list.active {
    opacity: 1;
    transform: translateX(0);
}

.project-item {
    padding: 1rem;
    border-bottom: 1px solid #ecf0f1;
    transition: background 0.2s ease;
    cursor: pointer;
}

.project-item:hover {
    background: #f8f9fa;
}

.project-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.project-company {
    font-size: 0.85rem;
    color: #7f8c8d;
    margin-bottom: 0.25rem;
}

.project-manager {
    font-size: 0.85rem;
    color: #95a5a6;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 12px;
    width: 80%;
    max-width: 800px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
}

.modal-header {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    font-size: 1.3rem;
    font-weight: 600;
}

.close {
    font-size: 2rem;
    cursor: pointer;
    transition: opacity 0.2s ease;
}

.close:hover {
    opacity: 0.7;
}

.modal-body {
    padding: 2rem;
    max-height: 60vh;
    overflow-y: auto;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .dashboard-main {
        grid-template-columns: 300px 1fr 300px;
    }
}

@media (max-width: 768px) {
    .dashboard-main {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr auto;
    }
    
    .left-panels, .right-panels {
        flex-direction: row;
        overflow-x: auto;
    }
    
    .panel {
        min-width: 300px;
    }
}

/* Project Details Modal Styles */
.project-details {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.detail-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
}

.detail-section h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.detail-item label {
    font-weight: 600;
    color: #7f8c8d;
    font-size: 0.9rem;
}

.detail-item span {
    color: #2c3e50;
    font-size: 1rem;
}

.status-demolishing {
    color: #e74c3c;
    font-weight: bold;
}

.status-suspended {
    color: #f39c12;
    font-weight: bold;
}

.status-completed {
    color: #27ae60;
    font-weight: bold;
}

.video-section {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
}

.video-placeholder {
    background: linear-gradient(135deg, #34495e, #2c3e50);
    color: white;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
    width: 100%;
    max-width: 400px;
}

.video-placeholder i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.7;
}

.video-placeholder p {
    margin: 0.5rem 0;
}

/* Map Popup Styles */
.district-popup h3 {
    margin: 0 0 0.5rem 0;
    color: #2c3e50;
}

.district-popup p {
    margin: 0;
    color: #7f8c8d;
}

.project-popup h4 {
    margin: 0 0 0.5rem 0;
    color: #2c3e50;
}

.project-popup p {
    margin: 0.25rem 0;
    font-size: 0.9rem;
    color: #7f8c8d;
}

.project-popup button {
    background: #3498db;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 0.5rem;
    transition: background 0.2s ease;
}

.project-popup button:hover {
    background: #2980b9;
}

/* District Label Styles */
.district-label {
    background: none !important;
    border: none !important;
}

/* Custom Scrollbar */
.status-list::-webkit-scrollbar {
    width: 6px;
}

.status-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.status-list::-webkit-scrollbar-thumb {
    background: #bdc3c7;
    border-radius: 3px;
}

.status-list::-webkit-scrollbar-thumb:hover {
    background: #95a5a6;
}

/* Loading Animation */
@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

.loading {
    animation: pulse 1.5s ease-in-out infinite;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.panel {
    animation: fadeInUp 0.6s ease forwards;
}

.panel:nth-child(1) { animation-delay: 0.1s; }
.panel:nth-child(2) { animation-delay: 0.2s; }
.panel:nth-child(3) { animation-delay: 0.3s; }
.panel:nth-child(4) { animation-delay: 0.4s; }

/* Hover Effects */
.stat-card:hover .stat-icon {
    transform: scale(1.1);
    transition: transform 0.2s ease;
}

.project-item:hover {
    transform: translateX(5px);
    transition: transform 0.2s ease;
}

/* Status Indicators */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-indicator.demolishing {
    background: #e74c3c;
}

.status-indicator.suspended {
    background: #f39c12;
}

.status-indicator.completed {
    background: #27ae60;
}
