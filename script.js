// Dashboard Application
class DemolitionDashboard {
    constructor() {
        this.map = null;
        this.currentView = 'district';
        this.currentFilter = 'all';
        this.selectedProject = null;
        this.mockData = this.generateMockData();
        
        this.init();
    }

    init() {
        this.initializeMap();
        this.setupEventListeners();
        this.updateCurrentTime();
        this.loadOverviewStats();
        this.loadSupervisionData();
        this.loadSurveyData();
        this.loadProjectLists();
        
        // Update time every minute
        setInterval(() => this.updateCurrentTime(), 60000);
    }

    generateMockData() {
        const districts = [
            { name: '市南区', center: [36.0611, 120.3133], projects: 12 },
            { name: '市北区', center: [36.0875, 120.3742], projects: 18 },
            { name: '李沧区', center: [36.1450, 120.4330], projects: 15 },
            { name: '崂山区', center: [36.1073, 120.4689], projects: 8 },
            { name: '城阳区', center: [36.3073, 120.3964], projects: 22 },
            { name: '黄岛区', center: [35.9618, 120.1933], projects: 25 },
            { name: '即墨区', center: [36.3890, 120.4473], projects: 14 },
            { name: '胶州市', center: [36.2647, 120.0332], projects: 11 },
            { name: '平度市', center: [36.7868, 119.9609], projects: 9 },
            { name: '莱西市', center: [36.8889, 120.5178], projects: 7 }
        ];

        const projects = [];
        const statuses = ['demolishing', 'suspended', 'completed'];
        const companies = ['青岛建工集团', '中建八局', '中铁建工', '青岛城建', '海尔地产'];
        
        districts.forEach((district, districtIndex) => {
            for (let i = 0; i < district.projects; i++) {
                const lat = district.center[0] + (Math.random() - 0.5) * 0.1;
                const lng = district.center[1] + (Math.random() - 0.5) * 0.1;
                
                projects.push({
                    id: `project_${districtIndex}_${i}`,
                    name: `${district.name}拆除项目${i + 1}`,
                    district: district.name,
                    address: `${district.name}某某街道${i + 1}号`,
                    status: statuses[Math.floor(Math.random() * statuses.length)],
                    coordinates: [lat, lng],
                    constructionCompany: companies[Math.floor(Math.random() * companies.length)],
                    projectManager: `项目经理${i + 1}`,
                    supervisionCompany: '青岛监理公司',
                    chiefSupervisor: `总监理师${i + 1}`,
                    plannedArea: Math.floor(Math.random() * 50000) + 10000,
                    plannedBuildings: Math.floor(Math.random() * 20) + 5,
                    completedBuildings: Math.floor(Math.random() * 15) + 2,
                    contractAmount: Math.floor(Math.random() * 50000000) + 10000000,
                    maxBuildingArea: Math.floor(Math.random() * 5000) + 1000,
                    maxHeight: Math.floor(Math.random() * 100) + 20,
                    maxFloors: Math.floor(Math.random() * 30) + 5,
                    maxSpan: Math.floor(Math.random() * 50) + 10,
                    plannedStartDate: '2024-01-15',
                    actualStartDate: '2024-01-20',
                    plannedEndDate: '2024-12-31',
                    actualEndDate: null
                });
            }
        });

        return { districts, projects };
    }

    initializeMap() {
        // Initialize Leaflet map centered on Qingdao
        this.map = L.map('map').setView([36.0611, 120.3133], 10);
        
        // Add tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(this.map);

        this.updateMapView();
    }

    updateMapView() {
        // Clear existing markers
        this.map.eachLayer(layer => {
            if (layer instanceof L.Marker || layer instanceof L.CircleMarker) {
                this.map.removeLayer(layer);
            }
        });

        if (this.currentView === 'district') {
            this.showDistrictView();
        } else {
            this.showProjectView();
        }
    }

    showDistrictView() {
        this.mockData.districts.forEach(district => {
            const marker = L.circleMarker(district.center, {
                radius: Math.sqrt(district.projects) * 3,
                fillColor: '#3498db',
                color: '#2980b9',
                weight: 2,
                opacity: 1,
                fillOpacity: 0.7
            }).addTo(this.map);

            marker.bindPopup(`
                <div class="district-popup">
                    <h3>${district.name}</h3>
                    <p>项目数量: ${district.projects}</p>
                </div>
            `);

            // Add district label
            L.marker(district.center, {
                icon: L.divIcon({
                    className: 'district-label',
                    html: `<div style="text-align: center; font-weight: bold; color: white; text-shadow: 1px 1px 2px rgba(0,0,0,0.7);">${district.projects}</div>`,
                    iconSize: [30, 30]
                })
            }).addTo(this.map);

            marker.on('click', () => {
                this.currentView = 'project';
                document.querySelector('[data-view="project"]').classList.add('active');
                document.querySelector('[data-view="district"]').classList.remove('active');
                this.updateMapView();
                this.map.setView(district.center, 12);
            });
        });
    }

    showProjectView() {
        const filteredProjects = this.currentFilter === 'all' 
            ? this.mockData.projects 
            : this.mockData.projects.filter(p => p.status === this.currentFilter);

        filteredProjects.forEach(project => {
            const color = this.getStatusColor(project.status);
            
            const marker = L.circleMarker(project.coordinates, {
                radius: 8,
                fillColor: color,
                color: '#fff',
                weight: 2,
                opacity: 1,
                fillOpacity: 0.8
            }).addTo(this.map);

            marker.bindPopup(`
                <div class="project-popup">
                    <h4>${project.name}</h4>
                    <p><strong>状态:</strong> ${this.getStatusText(project.status)}</p>
                    <p><strong>施工单位:</strong> ${project.constructionCompany}</p>
                    <button onclick="dashboard.showProjectDetail('${project.id}')">查看详情</button>
                </div>
            `);

            marker.on('click', () => {
                this.selectedProject = project;
                this.updatePanelsForProject(project);
            });
        });
    }

    getStatusColor(status) {
        const colors = {
            'demolishing': '#e74c3c',
            'suspended': '#f39c12',
            'completed': '#27ae60'
        };
        return colors[status] || '#95a5a6';
    }

    getStatusText(status) {
        const texts = {
            'demolishing': '正在拆除',
            'suspended': '暂停拆除',
            'completed': '已完成'
        };
        return texts[status] || '未知';
    }

    setupEventListeners() {
        // View toggle buttons
        document.querySelectorAll('[data-view]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('[data-view]').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                this.currentView = e.target.dataset.view;
                this.updateMapView();
            });
        });

        // Status filter
        document.getElementById('statusFilter').addEventListener('change', (e) => {
            this.currentFilter = e.target.value;
            this.updateMapView();
        });

        // Time period toggles
        document.querySelectorAll('.time-toggle .toggle-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const parent = e.target.closest('.time-toggle');
                parent.querySelectorAll('.toggle-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                
                // Update data based on period
                if (parent.closest('.supervision-panel')) {
                    this.loadSupervisionData(e.target.dataset.period);
                } else if (parent.closest('.survey-panel')) {
                    this.loadSurveyData(e.target.dataset.period);
                }
            });
        });

        // Status tabs
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                document.querySelectorAll('.status-list').forEach(l => l.classList.remove('active'));
                
                e.target.classList.add('active');
                document.getElementById(e.target.dataset.tab + 'List').classList.add('active');
            });
        });

        // Modal close
        document.querySelector('.close').addEventListener('click', () => {
            document.getElementById('projectModal').style.display = 'none';
        });

        window.addEventListener('click', (e) => {
            const modal = document.getElementById('projectModal');
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });
    }

    updateCurrentTime() {
        const now = new Date();
        const timeString = now.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
        document.getElementById('currentTime').textContent = timeString;
    }

    loadOverviewStats() {
        const stats = {
            constructionCompanies: 45,
            constructionPersonnel: 1250,
            supervisionCompanies: 12,
            supervisionPersonnel: 180,
            expertCount: 25,
            completedProjects: this.mockData.projects.filter(p => p.status === 'completed').length
        };

        Object.keys(stats).forEach(key => {
            this.animateNumber(document.getElementById(key), stats[key]);
        });
    }

    loadSupervisionData(period = 'monthly') {
        const data = period === 'monthly' ? {
            dailyInspections: 156,
            inspectedProjects: 89,
            safetyHazards: 23
        } : {
            dailyInspections: 1890,
            inspectedProjects: 1067,
            safetyHazards: 278
        };

        Object.keys(data).forEach(key => {
            this.animateNumber(document.getElementById(key), data[key]);
        });
    }

    loadSurveyData(period = 'monthly') {
        const totalProjects = this.mockData.projects.length;
        const plannedArea = this.mockData.projects.reduce((sum, p) => sum + p.plannedArea, 0);
        const plannedBuildings = this.mockData.projects.reduce((sum, p) => sum + p.plannedBuildings, 0);
        const completedBuildings = this.mockData.projects.reduce((sum, p) => sum + p.completedBuildings, 0);

        document.getElementById('totalProjects').textContent = totalProjects;
        document.getElementById('plannedArea').textContent = plannedArea.toLocaleString() + ' m²';
        document.getElementById('plannedBuildings').textContent = plannedBuildings;
        document.getElementById('completedBuildings').textContent = completedBuildings;
        
        const progress = (completedBuildings / plannedBuildings) * 100;
        document.getElementById('buildingProgress').style.width = progress + '%';
    }

    loadProjectLists() {
        const demolishingProjects = this.mockData.projects.filter(p => p.status === 'demolishing');
        const suspendedProjects = this.mockData.projects.filter(p => p.status === 'suspended');

        this.populateProjectList('demolishingList', demolishingProjects);
        this.populateProjectList('suspendedList', suspendedProjects);
    }

    populateProjectList(containerId, projects) {
        const container = document.getElementById(containerId);
        container.innerHTML = projects.map(project => `
            <div class="project-item" onclick="dashboard.showProjectDetail('${project.id}')">
                <div class="project-name">${project.name}</div>
                <div class="project-company">施工单位: ${project.constructionCompany}</div>
                <div class="project-manager">项目经理: ${project.projectManager}</div>
            </div>
        `).join('');
    }

    showProjectDetail(projectId) {
        const project = this.mockData.projects.find(p => p.id === projectId);
        if (!project) return;

        const modal = document.getElementById('projectModal');
        const modalBody = document.getElementById('modalBody');
        
        document.getElementById('modalProjectName').textContent = project.name;
        
        modalBody.innerHTML = `
            <div class="project-details">
                <div class="detail-section">
                    <h3>基本信息</h3>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>项目名称:</label>
                            <span>${project.name}</span>
                        </div>
                        <div class="detail-item">
                            <label>所在区域:</label>
                            <span>${project.district}</span>
                        </div>
                        <div class="detail-item">
                            <label>项目地址:</label>
                            <span>${project.address}</span>
                        </div>
                        <div class="detail-item">
                            <label>项目状态:</label>
                            <span class="status-${project.status}">${this.getStatusText(project.status)}</span>
                        </div>
                    </div>
                </div>
                
                <div class="detail-section">
                    <h3>参建单位</h3>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>施工单位:</label>
                            <span>${project.constructionCompany}</span>
                        </div>
                        <div class="detail-item">
                            <label>项目经理:</label>
                            <span>${project.projectManager}</span>
                        </div>
                        <div class="detail-item">
                            <label>监理单位:</label>
                            <span>${project.supervisionCompany}</span>
                        </div>
                        <div class="detail-item">
                            <label>总监理师:</label>
                            <span>${project.chiefSupervisor}</span>
                        </div>
                    </div>
                </div>
                
                <div class="detail-section">
                    <h3>工程概况</h3>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>计划拆除面积:</label>
                            <span>${project.plannedArea.toLocaleString()} m²</span>
                        </div>
                        <div class="detail-item">
                            <label>计划拆除建筑数:</label>
                            <span>${project.plannedBuildings}</span>
                        </div>
                        <div class="detail-item">
                            <label>已完成拆除建筑数:</label>
                            <span>${project.completedBuildings}</span>
                        </div>
                        <div class="detail-item">
                            <label>合同投资额:</label>
                            <span>¥${project.contractAmount.toLocaleString()}</span>
                        </div>
                        <div class="detail-item">
                            <label>最大单体建筑面积:</label>
                            <span>${project.maxBuildingArea.toLocaleString()} m²</span>
                        </div>
                        <div class="detail-item">
                            <label>最大建筑高度:</label>
                            <span>${project.maxHeight} m</span>
                        </div>
                        <div class="detail-item">
                            <label>最大建筑层数:</label>
                            <span>${project.maxFloors} 层</span>
                        </div>
                        <div class="detail-item">
                            <label>最大单体建筑跨度:</label>
                            <span>${project.maxSpan} m</span>
                        </div>
                    </div>
                </div>
                
                <div class="detail-section">
                    <h3>工期信息</h3>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>计划开工日期:</label>
                            <span>${project.plannedStartDate}</span>
                        </div>
                        <div class="detail-item">
                            <label>实际开工日期:</label>
                            <span>${project.actualStartDate}</span>
                        </div>
                        <div class="detail-item">
                            <label>计划竣工日期:</label>
                            <span>${project.plannedEndDate}</span>
                        </div>
                        <div class="detail-item">
                            <label>实际竣工日期:</label>
                            <span>${project.actualEndDate || '进行中'}</span>
                        </div>
                    </div>
                </div>
                
                <div class="detail-section">
                    <h3>实时监控</h3>
                    <div class="video-section">
                        <div class="video-placeholder">
                            <i class="fas fa-video"></i>
                            <p>实时视频监控</p>
                            <p>摄像头 1 - 施工现场全景</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        modal.style.display = 'block';
    }

    updatePanelsForProject(project) {
        // This method would update side panels with project-specific data
        // For now, we'll just highlight the selected project
        console.log('Selected project:', project.name);
    }

    animateNumber(element, targetValue) {
        const startValue = 0;
        const duration = 1000;
        const startTime = performance.now();
        
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            const currentValue = Math.floor(startValue + (targetValue - startValue) * progress);
            element.textContent = currentValue.toLocaleString();
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        
        requestAnimationFrame(animate);
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new DemolitionDashboard();
});
